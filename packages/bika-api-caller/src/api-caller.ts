import { errors } from '@bika/contents/config/server/error/errors';
import { Mutation<PERSON>ache, QueryCache, QueryClient } from '@tanstack/react-query';
import { httpBatchLink, TRPCClientError, type TRPCLink } from '@trpc/client';
import { observable } from '@trpc/server/observable';
import axios, { type AxiosInstance } from 'axios';
import { EventSource } from 'eventsource';
import type toastFunc from 'react-hot-toast';
import { z } from 'zod';
import { API_ROOT_URL, type AppRouter, TRPC_ROOT_URL, type TRPCOriginClient, trpc } from './consts';
import { useApiUsageLimitErrorStore } from './error-store';

/**
 * Helper function to format ZodError using z.prettifyError for better user experience
 * @param error TRPCClientError that might contain ZodError data
 * @returns Formatted error message or null if no ZodError found
 */
function formatZodErrorMessage(error: TRPCClientError): string | null {
  // Check if error contains zodError data (from BAD_REQUEST responses)
  if (error?.data?.zodError) {
    try {
      // Reconstruct ZodError from the flattened data
      const zodError = new z.ZodError([]);
      // Add issues from the flattened zodError data
      if (error.data.zodError.formErrors) {
        for (const formError of error.data.zodError.formErrors) {
          zodError.addIssue({
            code: 'custom',
            message: formError,
            path: [],
          });
        }
      }
      if (error.data.zodError.fieldErrors) {
        for (const [field, fieldErrors] of Object.entries(error.data.zodError.fieldErrors)) {
          if (Array.isArray(fieldErrors)) {
            for (const fieldError of fieldErrors) {
              zodError.addIssue({
                code: 'custom',
                message: fieldError,
                path: [field],
              });
            }
          }
        }
      }
      return z.prettifyError(zodError);
    } catch (e) {
      console.warn('Failed to format ZodError:', e);
      return null;
    }
  }

  // Check if error data contains formatted ZodError data (from INTERNAL_SERVER_ERROR responses)
  if (error?.data?.code === errors.common.data_schema_validation.code && error?.data?.data) {
    try {
      // The server already formatted the ZodError, we can use z.prettifyError on a reconstructed error
      const zodError = new z.ZodError([]);
      const formattedData = error.data.data;

      // Recursively extract error messages from the formatted data structure
      function extractErrorsFromFormatted(obj: any, path: string[] = []): void {
        if (typeof obj === 'string') {
          zodError.addIssue({
            code: 'custom',
            message: obj,
            path,
          });
        } else if (Array.isArray(obj)) {
          for (const item of obj) {
            if (typeof item === 'string') {
              zodError.addIssue({
                code: 'custom',
                message: item,
                path,
              });
            }
          }
        } else if (obj && typeof obj === 'object') {
          for (const [key, value] of Object.entries(obj)) {
            if (key !== '_errors') {
              extractErrorsFromFormatted(value, [...path, key]);
            } else if (Array.isArray(value)) {
              for (const error of value) {
                if (typeof error === 'string') {
                  zodError.addIssue({
                    code: 'custom',
                    message: error,
                    path,
                  });
                }
              }
            }
          }
        }
      }

      extractErrorsFromFormatted(formattedData);
      return z.prettifyError(zodError);
    } catch (e) {
      console.warn('Failed to format server ZodError:', e);
      return null;
    }
  }

  return null;
}

export class APICaller {
  private _basePath: string;

  private _trpcURL: string;

  private _toast?: typeof toastFunc;

  // App API
  private _apiURL: string;

  private _trpcClient?: TRPCOriginClient;

  private _headers: Record<string, string> | undefined;

  private _reactQueryClient?: QueryClient;

  public get trpcClient(): TRPCOriginClient {
    if (!this._trpcClient) {
      const url = this._trpcURL;
      const headers = this._headers;
      // const toast = this._toast;
      const errorHandlingLink: TRPCLink<AppRouter> =
        () =>
        ({ next, op }) =>
          observable((observer) => {
            const unsubscribe = next(op).subscribe({
              next(value) {
                observer.next(value);
              },
              error(error) {
                // if (error.data) {
                //   const errorMessage = error.data.message;
                //   // 可根据特定的错误码，不做默认toast提示, 在外面组件里自己提示
                //   toast?.error(errorMessage);
                //   if (error.data.code === errors.billing.usage_exceed_limit.code) {
                //     // 使用限制错误，设置到store中
                //     useApiUsageLimitErrorStore.getState().setError(errorMessage);
                //   }
                // } else {
                //   toast?.error(error.message);
                // }
                observer.error(error);
              },
              complete() {
                observer.complete();
              },
            });
            return unsubscribe;
          });
      this._trpcClient = trpc.createClient({
        links: [errorHandlingLink, httpBatchLink({ url, headers })],
      });
    }
    return this._trpcClient!;
  }

  /**
   * Get the axios client directly
   */
  public get axios() {
    return axios;
  }

  /**
   * 创建 新的Sse连接，客户端
   *
   * @returns
   */
  public newSse() {
    const onlineSessionId = APICaller.genId();
    return {
      eventSource: new EventSource(`${this._basePath}/api/sse/${onlineSessionId}`, {
        // headers: this._headers,
        fetch: (input, init) =>
          fetch(input, {
            ...init,
            headers: {
              ...(init?.headers || {}),
              ...this._headers,
            },
          }),
      }),
      onlineSessionId,
    };
  }

  public get subscriber() {
    const onlineSessionId = APICaller.genId();
    return new EventSource(`${this._basePath}/api/sse/subsribe/${onlineSessionId}`, {
      // headers: this._headers,
      fetch: (input, init) =>
        fetch(input, {
          ...init,
          headers: {
            ...(init?.headers || {}),
            ...this._headers,
          },
        }),
    });
  }

  private _axiosClient?: AxiosInstance;

  /**
   * 应用程序API的axios客户端
   */
  public get apiClient() {
    if (this._axiosClient) return this._axiosClient!;

    this._axiosClient = axios.create({
      baseURL: this._apiURL, // Set the base URL for the requests
      timeout: 5000, // Set a timeout (in milliseconds) for the requests
      headers: this._headers,
    });
    return this._axiosClient!;
  }

  /**
   * 获取边缘服务配置，中心中控配置
   */
  public async getEdgeConfig(): Promise<{
    // 主网站地址
    url: string;
    // 中国版地址
    cnUrl: string;

    // 当前线上版本，如果运行环境的版本，大于线上版本，可理解为审核环境
    version: string;
  }> {
    const url = 'https://edge.bika.ai/api/config';
    const res = await this.axios.get(url);
    return res.data;
  }

  public get reactQueryClient() {
    if (!this._reactQueryClient)
      this._reactQueryClient = new QueryClient({
        defaultOptions: {
          queries: {
            retry: 3,
            refetchOnWindowFocus: false,
          },
        },
        // 下面是防止覆盖defaultOptions配置导致全局错误处理失效, 效果跟上面一样
        queryCache: new QueryCache({
          onError: (error, query) => {
            // catch query error in one place
            // console.error('global query error:', error);
            const skipError = query.meta?.skipError;
            if (skipError) return;
            if (error instanceof TRPCClientError) {
              if (error?.data?.code) {
                // Try to format ZodError first for better user experience
                const zodErrorMessage = formatZodErrorMessage(error);
                const errorMessage = zodErrorMessage || String(error?.data?.message);

                if (error?.data?.code === errors.billing.usage_exceed_limit.code) {
                  // 使用限制错误，设置到store中
                  useApiUsageLimitErrorStore.getState().setError(errorMessage);
                } else {
                  // 可根据特定的错误码，不做默认toast提示, 在外面组件里自己提示
                  this._toast?.error(errorMessage);
                }
              }
              // if (error?.data.httpStatus === 401) {
              //   // 401 未授权，跳转到登录页
              //   window.location.href = '/login';
              // }
            } else {
              this._toast?.error(error.message);
            }
          },
        }),
        mutationCache: new MutationCache({
          onError: (error, _variables, _context, mutation) => {
            // catch mutation error in one place
            // console.error('global mutate error:', error);
            const skipError = mutation.options.meta?.skipError;
            if (skipError) return;
            if (error instanceof TRPCClientError) {
              if (error?.data?.code) {
                // Try to format ZodError first for better user experience
                const zodErrorMessage = formatZodErrorMessage(error);
                const errorMessage = zodErrorMessage || String(error?.data?.message);

                if (error?.data?.code === errors.billing.usage_exceed_limit.code) {
                  // 使用限制错误，设置到store中
                  useApiUsageLimitErrorStore.getState().setError(errorMessage);
                } else {
                  // 可根据特定的错误码，不做默认toast提示, 在外面组件里自己提示
                  this._toast?.error(errorMessage);
                }
              }
            } else {
              this._toast?.error(error.message);
            }
          },
        }),
      });

    return this._reactQueryClient;
  }

  /**
   * API服务器的完整地址传入
   *
   * basePath默认为空，即用同个域名
   * 你可以传入不同的域名
   * @param bikaBasePath 通常不带/api，只有域名，如https://bika.ai, http://localhost:3000
   * @param headers
   */
  constructor({
    bikaBasePath,
    headers,
    toast,
  }: {
    bikaBasePath?: string;
    headers?: Record<string, string>;
    toast?: typeof toastFunc;
  }) {
    this._basePath = bikaBasePath || '';
    this._trpcURL = this._basePath + TRPC_ROOT_URL;
    this._apiURL = this._basePath + API_ROOT_URL;
    this._headers = headers;
    this._toast = toast;
  }

  /**
   * 获取服务器延迟的ms毫秒
   *
   * @returns
   */
  async pingLattency() {
    const startTime = new Date().getTime();
    await this.apiClient.get('/meta'); // /api/meta
    const endTime = new Date().getTime();
    const latency = endTime - startTime;
    return latency;
  }

  static genId() {
    return `ose${new Date().getTime()}${APICaller.generateRandomString(10)}`;
  }

  static generateRandomString(length: number) {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let randomString = '';

    for (let i = 0; i < length; i += 1) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      randomString += characters.charAt(randomIndex);
    }

    return randomString;
  }
}
